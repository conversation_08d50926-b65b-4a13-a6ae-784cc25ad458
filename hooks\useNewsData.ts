/**
 * Custom hook for managing news data state and operations
 * Centralizes all news-related data fetching, state management, and operations
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { NewsData, NewsItem } from '@/types/news';
import { newsService } from '@/services/newsService';
import { ApiError } from '@/services/apiClient';
import { toast } from 'sonner';

/**
 * Hook return type definition
 */
interface UseNewsDataReturn {
  // State
  newsData: NewsData;
  loading: boolean;
  error: string | null;

  // Operations
  fetchNews: () => Promise<void>;
  resetAllNews: () => Promise<void>;
  deleteNews: (key: string) => void;

  // Utility
  refreshData: () => Promise<void>;
  clearError: () => void;
}

/**
 * Options for the useNewsData hook
 */
interface UseNewsDataOptions {
  autoFetch?: boolean;
  onSuccess?: (data: NewsData) => void;
  onError?: (error: string) => void;
}

/**
 * Custom hook for managing news data
 * @param options Configuration options for the hook
 * @returns Object containing news data state and operations
 */
export function useNewsData(options: UseNewsDataOptions = {}): UseNewsDataReturn {
  const { autoFetch = true, onSuccess, onError } = options;

  // State management
  const [newsData, setNewsData] = useState<NewsData>({});
  const [loading, setLoading] = useState(autoFetch);
  const [error, setError] = useState<string | null>(null);

  // Ref to track if component is mounted (prevents state updates after unmount)
  const isMountedRef = useRef(true);

  /**
   * Safely updates state only if component is still mounted
   */
  const safeSetState = useCallback(<T>(setter: (value: T) => void, value: T) => {
    if (isMountedRef.current) {
      setter(value);
    }
  }, []);

  /**
   * Handles errors consistently across all operations
   */
  const handleError = useCallback((error: unknown, operation: string) => {
    let errorMessage: string;

    if (error instanceof ApiError) {
      errorMessage = `${operation} failed: ${error.message}`;
      if (error.status) {
        errorMessage += ` (HTTP ${error.status})`;
      }
    } else if (error instanceof Error) {
      errorMessage = `${operation} failed: ${error.message}`;
    } else {
      errorMessage = `${operation} failed: Unknown error occurred`;
    }

    console.error(`News operation error [${operation}]:`, error);

    safeSetState(setError, errorMessage);
    onError?.(errorMessage);

    toast.error(`Error: ${operation}`, {
      description: errorMessage,
    });
  }, [safeSetState, onError]);

  /**
   * Clears the current error state
   */
  const clearError = useCallback(() => {
    safeSetState(setError, null);
  }, [safeSetState]);

  /**
   * Fetches all news data from the service
   */
  const fetchNews = useCallback(async () => {
    safeSetState(setLoading, true);
    safeSetState(setError, null);

    try {
      const data = await newsService.fetchAllNews();

      // Validate the received data
      newsService.validateNewsData(data);

      safeSetState(setNewsData, data);
      onSuccess?.(data);

      const newsCount = Object.keys(data).length;
      if (newsCount > 0) {
        toast.success(
          `${newsCount} news ${newsCount === 1 ? 'item' : 'items'} loaded successfully!`
        );
      }
    } catch (error) {
      handleError(error, 'Loading news');
    } finally {
      safeSetState(setLoading, false);
    }
  }, [safeSetState, onSuccess, handleError]);

  /**
   * Resets all news data (deletes everything)
   */
  const resetAllNews = useCallback(async () => {
    safeSetState(setLoading, true);
    safeSetState(setError, null);

    try {
      await newsService.resetAllNews();

      // Clear local state
      safeSetState(setNewsData, {});

      toast.success('All news items have been removed successfully!', {
        description: 'The database has been completely reset.',
      });
    } catch (error) {
      handleError(error, 'Resetting news');
    } finally {
      safeSetState(setLoading, false);
    }
  }, [safeSetState, handleError]);

  /**
   * Deletes a specific news item by key
   * Note: This only updates local state. The actual deletion should be handled
   * by the component using useNewsActions hook for consistency.
   */
  const deleteNews = useCallback((key: string) => {
    if (!key || typeof key !== 'string') {
      console.warn('Invalid news key provided to deleteNews');
      return;
    }

    safeSetState(setNewsData, (prevData) => {
      const newData = { ...prevData };
      delete newData[key];
      return newData;
    });
  }, [safeSetState]);

  /**
   * Refreshes the news data (alias for fetchNews for clarity)
   */
  const refreshData = useCallback(async () => {
    await fetchNews();
  }, [fetchNews]);

  // Auto-fetch news on mount if enabled
  useEffect(() => {
    if (autoFetch) {
      fetchNews();
    }

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMountedRef.current = false;
    };
  }, [autoFetch, fetchNews]);

  // Return the hook interface
  return {
    // State
    newsData,
    loading,
    error,

    // Operations
    fetchNews,
    resetAllNews,
    deleteNews,

    // Utility
    refreshData,
    clearError,
  };
}

/**
 * Type exports for use in other modules
 */
export type { UseNewsDataReturn, UseNewsDataOptions };
