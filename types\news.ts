export interface NewsItem {
  newsAuthor: string;
  newsAuthorImg: string;
  newsAuthorImgDark: string;
  newsContent: string;
  newsDate: string;
  newsImg: string;
  newsTitle: string;
  newsUrl: string;
  selected: boolean;
}

export interface NewsData {
  [key: string]: NewsItem;
}

/**
 * Request body for sync API endpoint (search terms)
 */
export interface SyncRequest {
  search_terms: string[];
  time_period: string;
  days_filter: number;
  use_debug_firebase: boolean;
  light: boolean;
}

/**
 * Request body for excluded-sources API endpoint
 */
export interface ExcludedSourcesRequest {
  sources: string[];
  action: "add" | "remove";
  use_debug: boolean;
}

/**
 * Generic API response
 */
export interface ApiResponse {
  success: boolean;
  message?: string;
  data?: any;
}