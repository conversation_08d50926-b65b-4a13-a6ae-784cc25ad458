# EasyPanel Deployment Guide for CBNEWS Admin

## Prerequisites

1. **EasyPanel Account**: Ensure you have access to your EasyPanel dashboard
2. **Git Repository**: Your code should be in a Git repository (GitHub, GitLab, etc.)
3. **Environment Variables**: Prepare your production environment variables

## Deployment Steps

### 1. Prepare Your Repository

Ensure these files are in your repository root:
- `Dockerfile` ✅ (Created)
- `.dockerignore` ✅ (Created)
- `.env.production` ✅ (Template created)
- `healthcheck.js` ✅ (Created)

### 2. EasyPanel Configuration

#### A. Create New Service
1. Log into your EasyPanel dashboard
2. Click "Create Service" or "New App"
3. Choose "Docker" or "Git Repository" deployment method

#### B. Repository Settings
- **Repository URL**: Your Git repository URL
- **Branch**: `main` or your production branch
- **Build Context**: `/` (root directory)
- **Dockerfile Path**: `./Dockerfile`

#### C. Environment Variables
Set these environment variables in EasyPanel:

```bash
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
NEXT_PUBLIC_FIREBASE_DB_URL=https://debug-cnews-default-rtdb.firebaseio.com
NEXT_PUBLIC_CNEWS_API_URL=https://side-projects-cnews-api.vhhb1z.easypanel.host/api/v1/news
NEXT_PUBLIC_API_TIMEOUT=15000
NEXT_PUBLIC_API_RETRY_ATTEMPTS=3
PORT=3000
```

#### D. Port Configuration
- **Internal Port**: 3000
- **External Port**: 80 or 443 (HTTPS)
- **Protocol**: HTTP

#### E. Health Check (Optional)
- **Health Check Path**: `/`
- **Health Check Port**: 3000

### 3. Domain Configuration

1. In EasyPanel, go to your service settings
2. Add your custom domain or use the provided subdomain
3. Enable SSL/TLS certificate (Let's Encrypt)

### 4. Build and Deploy

1. Click "Deploy" or "Build & Deploy"
2. Monitor the build logs for any errors
3. Once deployed, test your application

## Local Testing Before Deployment

### Option 1: Docker Build Test
```bash
# Build the Docker image
pnpm run docker:build

# Run the container locally
pnpm run docker:run
```

### Option 2: Docker Compose Test
```bash
# Create .env.local with your environment variables
cp .env.production .env.local

# Run with Docker Compose
pnpm run docker:dev
```

### Option 3: Production Build Test
```bash
# Install dependencies
pnpm install

# Build for production
pnpm run build:production

# Start production server
pnpm run start:production
```

## Troubleshooting

### Common Issues

1. **Build Fails**
   - Check that all dependencies are in package.json
   - Verify environment variables are set correctly
   - Check Docker build logs in EasyPanel

2. **Application Won't Start**
   - Verify PORT environment variable is set to 3000
   - Check that health check is passing
   - Review application logs in EasyPanel

3. **API Connection Issues**
   - Verify NEXT_PUBLIC_CNEWS_API_URL is correct
   - Check if API endpoint is accessible from your VPS
   - Verify Firebase URL is correct

4. **Static Assets Not Loading**
   - Ensure `output: 'standalone'` is set in next.config.js
   - Check that public folder is being copied correctly

### Monitoring

- Use EasyPanel's built-in monitoring
- Check application logs regularly
- Monitor resource usage (CPU, Memory)
- Set up alerts for downtime

## Performance Optimization

1. **Enable Compression**: Already enabled in next.config.js
2. **Image Optimization**: Configured in next.config.js
3. **Caching**: Consider adding Redis for session/data caching
4. **CDN**: Use a CDN for static assets if needed

## Security Considerations

1. **Environment Variables**: Never commit sensitive data to Git
2. **HTTPS**: Always use HTTPS in production
3. **Headers**: Consider adding security headers
4. **Updates**: Keep dependencies updated regularly

## Backup Strategy

1. **Database**: Ensure Firebase data is backed up
2. **Environment**: Keep environment variables documented
3. **Code**: Use Git tags for releases

## Support

If you encounter issues:
1. Check EasyPanel documentation
2. Review application logs
3. Test locally with Docker first
4. Contact EasyPanel support if needed
