/**
 * Custom hook for managing news filtering and sorting logic
 * Provides optimized filtering with memoization and stable references
 */

import { useState, useMemo, useCallback, useEffect } from 'react';
import { NewsData, NewsItem } from '@/types/news';
import { MAJOR_NEWS_SOURCES, FILTER_VALUES } from '@/lib/constants';
import { parseBrazilianDate } from '@/lib/utils';

/**
 * Filter configuration interface
 */
interface NewsFilters {
  search: string;
  author: string;
  date: Date | undefined;
  sort: string;
}

/**
 * Hook return type definition
 */
interface UseNewsFiltersReturn {
  // Filter state
  filters: NewsFilters;

  // Filter setters
  setSearch: (search: string) => void;
  setAuthor: (author: string) => void;
  setDate: (date: Date | undefined) => void;
  setSort: (sort: string) => void;

  // Utility functions
  clearFilters: () => void;
  hasActiveFilters: boolean;

  // Filtered data
  filteredNews: [string, NewsItem][];
  filteredCount: number;

  // Derived data
  uniqueAuthors: <AUTHORS>
}

/**
 * Default filter values
 */
const DEFAULT_FILTERS: NewsFilters = {
  search: '',
  author: FILTER_VALUES.PRINCIPAIS,
  date: undefined,
  sort: 'date-desc',
};

/**
 * Custom hook for news filtering and sorting
 * @param newsData The news data to filter
 * @returns Object containing filter state and filtered results
 */
export function useNewsFilters(newsData: NewsData): UseNewsFiltersReturn {
  const [filters, setFilters] = useState<NewsFilters>(DEFAULT_FILTERS);

  /**
   * Memoized news array to prevent unnecessary recalculations
   */
  const newsArray = useMemo(() => {
    return Object.entries(newsData);
  }, [newsData]);

  /**
   * Memoized unique authors list
   */
  const uniqueAuthors = useMemo(() => {
    const authors = Object.values(newsData).map((item) => item.newsAuthor);
    return Array.from(new Set(authors)).sort();
  }, [newsData]);

  /**
   * Filter function for search text
   */
  const searchFilter = useCallback((item: NewsItem, searchTerm: string): boolean => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      item.newsTitle?.toLowerCase().includes(searchLower) ||
      item.newsContent?.toLowerCase().includes(searchLower) ||
      item.newsAuthor?.toLowerCase().includes(searchLower)
    );
  }, []);

  /**
   * Filter function for author
   */
  const authorFilter = useCallback((item: NewsItem, authorName: string): boolean => {
    if (!authorName || authorName === FILTER_VALUES.ALL) return true;
    if (authorName === FILTER_VALUES.PRINCIPAIS) {
      return MAJOR_NEWS_SOURCES.includes(item.newsAuthor as any);
    }
    return item.newsAuthor === authorName;
  }, []);

  /**
   * Filter function for date
   */
  const dateFilter = useCallback((item: NewsItem, filterDate: Date | undefined): boolean => {
    if (!filterDate) return true;

    const itemDate = parseBrazilianDate(item.newsDate);
    if (!itemDate) return false;

    return itemDate.toDateString() === filterDate.toDateString();
  }, []);

  /**
   * Sort function for news items
   */
  const sortNews = useCallback((a: [string, NewsItem], b: [string, NewsItem], sortType: string): number => {
    const [, itemA] = a;
    const [, itemB] = b;

    switch (sortType) {
      case 'date-asc': {
        const dateA = parseBrazilianDate(itemA.newsDate);
        const dateB = parseBrazilianDate(itemB.newsDate);
        if (!dateA || !dateB) return 0;
        return dateA.getTime() - dateB.getTime();
      }
      case 'date-desc': {
        const dateA = parseBrazilianDate(itemA.newsDate);
        const dateB = parseBrazilianDate(itemB.newsDate);
        if (!dateA || !dateB) return 0;
        return dateB.getTime() - dateA.getTime();
      }
      case 'author':
        return itemA.newsAuthor.localeCompare(itemB.newsAuthor);
      case 'title':
        return (itemA.newsTitle || '').localeCompare(itemB.newsTitle || '');
      default:
        return 0;
    }
  }, []);

  /**
   * Memoized filtered and sorted news
   */
  const filteredNews = useMemo(() => {
    let result = newsArray;

    // Apply search filter
    if (filters.search) {
      result = result.filter(([_, item]) => searchFilter(item, filters.search));
    }

    // Apply author filter - always apply if author is set (including "principais")
    if (filters.author) {
      result = result.filter(([_, item]) => authorFilter(item, filters.author));
    }

    // Apply date filter
    if (filters.date) {
      result = result.filter(([_, item]) => dateFilter(item, filters.date));
    }

    // Apply sorting
    result.sort((a, b) => sortNews(a, b, filters.sort));

    return result;
  }, [newsArray, filters, searchFilter, authorFilter, dateFilter, sortNews]);

  /**
   * Check if any filters are active
   */
  const hasActiveFilters = useMemo(() => {
    return !!(filters.search || filters.author || filters.date);
  }, [filters.search, filters.author, filters.date]);

  /**
   * Filter setter functions with stable references
   */
  const setSearch = useCallback((search: string) => {
    setFilters(prev => ({ ...prev, search }));
  }, []);

  const setAuthor = useCallback((author: string) => {
    setFilters(prev => ({ ...prev, author }));
  }, []);

  const setDate = useCallback((date: Date | undefined) => {
    setFilters(prev => ({ ...prev, date }));
  }, []);

  const setSort = useCallback((sort: string) => {
    setFilters(prev => ({ ...prev, sort }));
  }, []);

  /**
   * Clear all filters
   */
  const clearFilters = useCallback(() => {
    setFilters(DEFAULT_FILTERS);
  }, []);

  return {
    // Filter state
    filters,

    // Filter setters
    setSearch,
    setAuthor,
    setDate,
    setSort,

    // Utility functions
    clearFilters,
    hasActiveFilters,

    // Filtered data
    filteredNews,
    filteredCount: filteredNews.length,

    // Derived data
    uniqueAuthors,
  };
}

/**
 * Type exports for use in other modules
 */
export type { NewsFilters, UseNewsFiltersReturn };
