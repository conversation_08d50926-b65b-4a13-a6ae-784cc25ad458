# Fix para Múltiplas Requisições no /process-sync

## Problema Identificado

O sistema estava enviando múltiplas requisições para o endpoint `/process-sync` devido a:

1. **Timeout muito baixo (15s)** para um processamento síncrono que pode demorar mais
2. **Sistema de retry ativo (3 tentativas)** que causava reenvios desnecessários
3. **Falta de proteção contra cliques múltiplos** nos componentes de UI
4. **Mensagem de erro confusa** do AbortController

## Soluções Implementadas

### 1. Configuração Específica para /process-sync

**Arquivo:** `services/newsService.ts`

- **Timeout aumentado**: 60 segundos (era 15s)
- **Retry desabilitado**: 0 tentativas (era 3)
- **Justificativa**: O endpoint `/process-sync` é um processamento síncrono que pode demorar, não deve ser retriado

```typescript
await apiClient.post(url, { url: newsUrl }, {
  timeout: 60000, // 60 seconds timeout
  retries: 0      // No retries to prevent duplicate processing
});
```

### 2. Proteção Contra Múltiplas Chamadas

**Arquivo:** `hooks/useNewsActions.ts`

- **Verificação de estado**: Impede nova chamada se já está enviando
- **Feedback ao usuário**: Toast de aviso quando tentativa duplicada
- **Dependência atualizada**: `isSending` adicionado às dependências do useCallback

```typescript
// Prevent multiple simultaneous sends
if (isSending) {
  toast.warning('Send operation already in progress', {
    description: 'Please wait for the current operation to complete.',
  });
  return;
}
```

### 3. Melhoria no Tratamento de Timeout

**Arquivo:** `services/apiClient.ts`

- **Mensagem de erro mais clara**: Especifica que foi timeout
- **AbortController melhorado**: Passa razão específica para o abort
- **Lógica de retry refinada**: Não tenta novamente em caso de timeout quando retries = 0

```typescript
const timeoutId = setTimeout(() => {
  controller.abort(new Error(`Request timeout after ${timeout}ms`));
}, timeout);

// Check if it's an abort error (timeout)
if (error instanceof Error && error.name === 'AbortError') {
  lastError = new Error(`Request timeout after ${timeout}ms`);
}
```

### 4. Proteção na Interface

**Arquivo:** `components/news-actions.tsx`

- **Handler dedicado**: Função específica para tratar cliques no botão
- **Verificação dupla**: Impede ação se já está processando
- **Feedback imediato**: Toast de aviso para tentativas duplicadas

```typescript
const handleSend = () => {
  if (isSending) {
    toast.warning("Envio já em andamento", {
      description: "Aguarde a conclusão da operação atual.",
    });
    return;
  }
  onSend();
};
```

## Benefícios das Mudanças

1. **Eliminação de requisições duplicadas**: Apenas uma requisição por URL
2. **Melhor experiência do usuário**: Feedback claro sobre o status da operação
3. **Timeout adequado**: 60s permite processamento completo
4. **Mensagens de erro claras**: Usuário entende o que aconteceu
5. **Performance melhorada**: Menos carga no servidor

## Configurações Atuais

- **Timeout padrão**: 15s (outros endpoints)
- **Timeout /process-sync**: 60s
- **Retry padrão**: 3 tentativas
- **Retry /process-sync**: 0 tentativas

## Monitoramento

Para verificar se o fix está funcionando:

1. Observe os logs do console - não deve haver mais "Request failed after 4 attempts"
2. Verifique se apenas uma requisição é enviada por URL no Network tab
3. Confirme que o toast de sucesso aparece após ~30-60s (tempo normal de processamento)
4. Teste cliques múltiplos rápidos - deve mostrar aviso de "operação em andamento"

## Próximos Passos (Opcionais)

1. **Implementar indicador de progresso**: Mostrar tempo decorrido durante o envio
2. **Adicionar cancelamento**: Permitir cancelar operação em andamento
3. **Logs estruturados**: Adicionar logging detalhado para debugging
4. **Configuração por ambiente**: Timeouts diferentes para dev/prod
