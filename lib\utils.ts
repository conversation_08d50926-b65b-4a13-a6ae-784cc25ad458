import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Parses a Brazilian date string (DD/MM/YYYY) into a Date object
 * @param dateString - Date string in DD/MM/YYYY format
 * @returns Date object or null if parsing fails
 */
export function parseBrazilianDate(dateString: string): Date | null {
  if (!dateString || typeof dateString !== 'string') {
    return null;
  }

  // Check if it's already in ISO format (YYYY-MM-DD) or other standard formats
  if (dateString.includes('-') || dateString.length > 10) {
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date;
  }

  // Parse DD/MM/YYYY format
  const parts = dateString.split('/');
  if (parts.length !== 3) {
    return null;
  }

  const day = parseInt(parts[0], 10);
  const month = parseInt(parts[1], 10);
  const year = parseInt(parts[2], 10);

  // Validate date parts
  if (isNaN(day) || isNaN(month) || isNaN(year)) {
    return null;
  }

  if (day < 1 || day > 31 || month < 1 || month > 12 || year < 1900) {
    return null;
  }

  // Create date object (month is 0-indexed in JavaScript)
  const date = new Date(year, month - 1, day);

  // Verify the date is valid (handles cases like 31/02/2025)
  if (date.getDate() !== day || date.getMonth() !== month - 1 || date.getFullYear() !== year) {
    return null;
  }

  return date;
}

/**
 * Formats a date for display with Brazilian locale
 * @param date - Date object to format
 * @param options - Intl.DateTimeFormatOptions
 * @returns Formatted date string
 */
export function formatBrazilianDate(date: Date, options?: Intl.DateTimeFormatOptions): string {
  const defaultOptions: Intl.DateTimeFormatOptions = {
    day: "2-digit",
    month: "short",
    year: "numeric",
  };

  return date.toLocaleDateString("pt-BR", { ...defaultOptions, ...options });
}
