/**
 * Custom hook for managing sync operations
 * Handles sequential API calls to /sync endpoint for "imoveis" and "imobiliario" search terms
 */

import { useState, useCallback, useRef } from 'react';
import { newsService } from '@/services/newsService';
import { SyncRequest, ApiResponse } from '@/types/news';
import { ApiError } from '@/services/apiClient';
import { toast } from 'sonner';

/**
 * Hook return type definition
 */
interface UseSyncReturn {
  // State
  isProcessing: boolean;
  error: string | null;
  
  // Operations
  executeSync: () => Promise<void>;
  clearError: () => void;
}

/**
 * Options for the useSync hook
 */
interface UseSyncOptions {
  onSuccess?: (results: ApiResponse[]) => void;
  onError?: (error: string) => void;
}

/**
 * Custom hook for managing sync operations
 * @param options Configuration options for the hook
 * @returns Object containing sync state and operations
 */
export function useSync(options: UseSyncOptions = {}): UseSyncReturn {
  const { onSuccess, onError } = options;

  // State management
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Ref to track if component is mounted (prevents state updates after unmount)
  const isMountedRef = useRef(true);

  /**
   * Safely updates state only if component is still mounted
   */
  const safeSetState = useCallback(<T>(setter: (value: T) => void, value: T) => {
    if (isMountedRef.current) {
      setter(value);
    }
  }, []);

  /**
   * Handles errors with consistent formatting and user feedback
   */
  const handleError = useCallback((error: unknown, context: string, customHandler?: (error: string) => void, additionalInfo?: string) => {
    let errorMessage = 'An unexpected error occurred';

    if (error instanceof ApiError) {
      errorMessage = `${context}: ${error.message}`;
      if (error.status) {
        errorMessage += ` (Status: ${error.status})`;
      }
    } else if (error instanceof Error) {
      errorMessage = `${context}: ${error.message}`;
    } else if (typeof error === 'string') {
      errorMessage = `${context}: ${error}`;
    }

    if (additionalInfo) {
      errorMessage += ` - ${additionalInfo}`;
    }

    safeSetState(setError, errorMessage);
    
    // Call custom error handler if provided
    customHandler?.(errorMessage);
    onError?.(errorMessage);

    // Show user-friendly toast notification
    toast.error('Sync Failed', {
      description: errorMessage,
    });

    console.error(`${context}:`, error);
  }, [safeSetState, onError]);

  /**
   * Clears the current error state
   */
  const clearError = useCallback(() => {
    safeSetState(setError, null);
  }, [safeSetState]);

  /**
   * Creates a sync request object
   */
  const createSyncRequest = useCallback((searchTerm: string): SyncRequest => {
    return {
      search_terms: [searchTerm],
      time_period: "qdr:w",
      days_filter: 0,
      use_debug_firebase: true,
      light: true
    };
  }, []);

  /**
   * Executes the sync operation for both search terms sequentially
   */
  const executeSync = useCallback(async () => {
    if (isProcessing) {
      toast.warning('Sync already in progress');
      return;
    }

    safeSetState(setIsProcessing, true);
    safeSetState(setError, null);

    const searchTerms = ['imoveis', 'imobiliario'];
    const results: ApiResponse[] = [];

    try {
      // Show initial toast
      toast.info('Starting sync...', {
        description: 'Processing search terms sequentially',
      });

      // Execute requests sequentially
      for (let i = 0; i < searchTerms.length; i++) {
        const searchTerm = searchTerms[i];
        
        try {
          toast.info(`Processing "${searchTerm}"...`, {
            description: `Step ${i + 1} of ${searchTerms.length}`,
          });

          const request = createSyncRequest(searchTerm);
          const response = await newsService.sync(request);
          
          results.push(response);

          toast.success(`"${searchTerm}" processed successfully`, {
            description: response.message || 'Request completed',
          });

        } catch (error) {
          // Handle individual request error but continue with next request
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          toast.error(`Failed to process "${searchTerm}"`, {
            description: errorMessage,
          });
          
          console.error(`Error processing ${searchTerm}:`, error);
          
          // Add error result to maintain sequence
          results.push({
            success: false,
            message: errorMessage,
          });
        }
      }

      // Check if any requests succeeded
      const successfulRequests = results.filter(r => r.success);
      
      if (successfulRequests.length > 0) {
        onSuccess?.(results);
        
        toast.success('Sync completed!', {
          description: `${successfulRequests.length} of ${searchTerms.length} requests succeeded`,
        });
      } else {
        throw new Error('All sync requests failed');
      }

    } catch (error) {
      handleError(error, 'Sync');
    } finally {
      safeSetState(setIsProcessing, false);
    }
  }, [isProcessing, safeSetState, createSyncRequest, handleError, onSuccess]);

  // Cleanup function to prevent state updates after unmount
  const cleanup = useCallback(() => {
    isMountedRef.current = false;
  }, []);

  // Return the hook interface
  return {
    // State
    isProcessing,
    error,
    
    // Operations
    executeSync,
    clearError,
  };
}

/**
 * Type exports for use in other modules
 */
export type { UseSyncReturn, UseSyncOptions };
