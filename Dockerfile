# Dockerfile simplificado para Next.js com PNPM
FROM node:20-alpine

# Instalar pnpm
RUN npm install -g pnpm

WORKDIR /app

# Copiar arquivos de dependências
COPY package.json pnpm-lock.yaml ./

# Instalar dependências
RUN pnpm install --frozen-lockfile

# Copiar código fonte
COPY . .

# Variáveis de ambiente
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000

# Build da aplicação
RUN pnpm build

# Expor porta
EXPOSE 3000

# Iniciar aplicação
CMD ["pnpm", "start"]