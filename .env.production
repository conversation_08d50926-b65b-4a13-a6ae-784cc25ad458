# Production Environment Configuration for EasyPanel Deployment
# Copy this file to .env.local for local production testing
# Set these variables in your EasyPanel environment

# Application Environment
NODE_ENV=production

# Firebase Configuration (Required)
NEXT_PUBLIC_FIREBASE_DB_URL=https://debug-cnews-default-rtdb.firebaseio.com

# External API Configuration
# Update this URL to your production API endpoint
NEXT_PUBLIC_CNEWS_API_URL=https://side-projects-cnews-api.vhhb1z.easypanel.host/api/v1/news

# API Configuration
NEXT_PUBLIC_API_TIMEOUT=15000
NEXT_PUBLIC_API_RETRY_ATTEMPTS=3

# Next.js Configuration
NEXT_TELEMETRY_DISABLED=1

# Port Configuration (EasyPanel will set this automatically)
PORT=3000

# Security Headers (Optional - EasyPanel can handle this)
# NEXT_PUBLIC_APP_URL=https://your-domain.com
