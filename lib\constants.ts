/**
 * Application constants and configuration values
 * Centralizes commonly used constants across the application
 */

/**
 * Major news sources that should be included in the "Principais" filter
 * These are considered high-priority, reliable news sources
 */
export const MAJOR_NEWS_SOURCES = [
  'O Globo',
  'O GLOBO',
  'GLOBO',
  'Globo',
  'G1',
  'Exame',
  'EXAME',
  'CBIC',
  'Folha de S.Paulo',
  'Estadão',
  'ESTADÃO',
  'E-INVESTIDOR',
  'UOL',
  'R7',
  'CNN Brasil',
  'CNN BRASIL',
  'Valor Econômico',
  'InfoMoney',
  'INFOMONEY',
  'Época',
  'GZH',
  'Veja',
  'IstoÉ',
  'Carta Capital',
  'El País Brasil',
  'BBC Brasil',
  'Reuters Brasil',
  'Agência Brasil',
  'Poder360',
  'Metrópoles',
  'METRÓPOLES',
  'Correio Braziliense',
  'Gazeta do Povo',
] as const;

/**
 * Special filter values used in the author filter dropdown
 */
export const FILTER_VALUES = {
  ALL: 'all',
  PRINCIPAIS: 'principais'
} as const;

/**
 * Filter labels for display in the UI
 */
export const FILTER_LABELS = {
  all: 'Todos os autores',
  principais: 'Principais'
} as const;

/**
 * Type definitions for filter values
 */
export type FilterValue = typeof FILTER_VALUES[keyof typeof FILTER_VALUES];
export type MajorNewsSource = typeof MAJOR_NEWS_SOURCES[number];
