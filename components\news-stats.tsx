"use client";

import { Card, CardContent } from "@/components/ui/card";
import { NewsData } from "@/types/news";
import { FileText, Users, Calendar, CheckCircle } from "lucide-react";
import { parseBrazilianDate } from "@/lib/utils";

interface NewsStatsProps {
  newsData: NewsData;
}

export function NewsStats({ newsData }: NewsStatsProps) {
  const newsArray = Object.values(newsData);
  const totalNews = newsArray.length;
  const selectedNews = newsArray.filter((item) => item.selected).length;
  const uniqueAuthors = new Set(newsArray.map((item) => item.newsAuthor)).size;

  // Calcular notícias por período
  const today = new Date();
  const todayNews = newsArray.filter((item) => {
    const itemDate = parseBrazilianDate(item.newsDate);
    return itemDate && itemDate.toDateString() === today.toDateString();
  }).length;

  const stats = [
    {
      title: "Total de Notícias",
      value: totalNews,
      icon: FileText,
      color: "bg-blue-500",
      bgColor: "bg-blue-50",
      textColor: "text-blue-700",
    },
    {
      title: "Autores Únicos",
      value: uniqueAuthors,
      icon: Users,
      color: "bg-green-500",
      bgColor: "bg-green-50",
      textColor: "text-green-700",
    },
    {
      title: "Publicadas Hoje",
      value: todayNews,
      icon: Calendar,
      color: "bg-orange-500",
      bgColor: "bg-orange-50",
      textColor: "text-orange-700",
    },
    {
      title: "Selecionadas",
      value: selectedNews,
      icon: CheckCircle,
      color: "bg-purple-500",
      bgColor: "bg-purple-50",
      textColor: "text-purple-700",
    },
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-4 sm:mb-6">
      {stats.map((stat, index) => (
        <Card
          key={index}
          className="border-0 shadow-xs hover:shadow-md transition-shadow"
        >
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center space-x-2 sm:space-x-3">
              <div
                className={`p-1.5 sm:p-2 rounded-lg ${stat.bgColor} flex-shrink-0`}
              >
                <stat.icon
                  className={`h-4 w-4 sm:h-5 sm:w-5 ${stat.textColor}`}
                />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-xs sm:text-sm font-medium text-gray-600 truncate">
                  {stat.title}
                </p>
                <p className="text-xl sm:text-2xl font-bold text-gray-900">
                  {stat.value}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
