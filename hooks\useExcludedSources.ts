/**
 * Custom hook for managing excluded sources operations
 * Handles API calls to /excluded-sources endpoint for adding/removing news authors
 */

import { useState, useCallback, useRef } from 'react';
import { newsService } from '@/services/newsService';
import { ExcludedSourcesRequest, ApiResponse } from '@/types/news';
import { ApiError } from '@/services/apiClient';
import { toast } from 'sonner';

/**
 * Hook return type definition
 */
interface UseExcludedSourcesReturn {
  // State
  isProcessing: boolean;
  error: string | null;
  
  // Operations
  addExcludedSources: (sources: string[]) => Promise<void>;
  removeExcludedSources: (sources: string[]) => Promise<void>;
  clearError: () => void;
}

/**
 * Options for the useExcludedSources hook
 */
interface UseExcludedSourcesOptions {
  onSuccess?: (response: ApiResponse, action: 'add' | 'remove', sources: string[]) => void;
  onError?: (error: string) => void;
  useDebug?: boolean;
}

/**
 * Custom hook for managing excluded sources operations
 * @param options Configuration options for the hook
 * @returns Object containing excluded sources state and operations
 */
export function useExcludedSources(options: UseExcludedSourcesOptions = {}): UseExcludedSourcesReturn {
  const { onSuccess, onError, useDebug = true } = options;

  // State management
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Ref to track if component is mounted (prevents state updates after unmount)
  const isMountedRef = useRef(true);

  /**
   * Safely updates state only if component is still mounted
   */
  const safeSetState = useCallback(<T>(setter: (value: T) => void, value: T) => {
    if (isMountedRef.current) {
      setter(value);
    }
  }, []);

  /**
   * Handles errors with consistent formatting and user feedback
   */
  const handleError = useCallback((error: unknown, context: string, customHandler?: (error: string) => void, additionalInfo?: string) => {
    let errorMessage = 'An unexpected error occurred';

    if (error instanceof ApiError) {
      errorMessage = `${context}: ${error.message}`;
      if (error.status) {
        errorMessage += ` (Status: ${error.status})`;
      }
    } else if (error instanceof Error) {
      errorMessage = `${context}: ${error.message}`;
    } else if (typeof error === 'string') {
      errorMessage = `${context}: ${error}`;
    }

    if (additionalInfo) {
      errorMessage += ` - ${additionalInfo}`;
    }

    safeSetState(setError, errorMessage);
    
    // Call custom error handler if provided
    customHandler?.(errorMessage);
    onError?.(errorMessage);

    // Show user-friendly toast notification
    toast.error('Excluded Sources Operation Failed', {
      description: errorMessage,
    });

    console.error(`${context}:`, error);
  }, [safeSetState, onError]);

  /**
   * Clears the current error state
   */
  const clearError = useCallback(() => {
    safeSetState(setError, null);
  }, [safeSetState]);

  /**
   * Creates an excluded sources request object
   */
  const createExcludedSourcesRequest = useCallback((sources: string[], action: 'add' | 'remove'): ExcludedSourcesRequest => {
    return {
      sources,
      action,
      use_debug: useDebug
    };
  }, [useDebug]);

  /**
   * Executes excluded sources operation
   */
  const executeExcludedSourcesOperation = useCallback(async (sources: string[], action: 'add' | 'remove') => {
    if (!sources || sources.length === 0) {
      toast.warning('No sources provided');
      return;
    }

    if (isProcessing) {
      toast.warning('Excluded sources operation already in progress');
      return;
    }

    safeSetState(setIsProcessing, true);
    safeSetState(setError, null);

    try {
      const actionText = action === 'add' ? 'Adding' : 'Removing';
      toast.info(`${actionText} excluded sources...`, {
        description: `Processing ${sources.length} source${sources.length > 1 ? 's' : ''}`,
      });

      const request = createExcludedSourcesRequest(sources, action);
      const response = await newsService.excludedSources(request);

      onSuccess?.(response, action, sources);

      const actionPastText = action === 'add' ? 'added to' : 'removed from';
      toast.success(`Sources ${actionPastText} excluded list!`, {
        description: response.message || `${sources.length} source${sources.length > 1 ? 's' : ''} processed successfully`,
      });

    } catch (error) {
      handleError(error, `${action === 'add' ? 'Add' : 'Remove'} Excluded Sources`);
    } finally {
      safeSetState(setIsProcessing, false);
    }
  }, [isProcessing, safeSetState, createExcludedSourcesRequest, handleError, onSuccess]);

  /**
   * Adds sources to the excluded list
   */
  const addExcludedSources = useCallback(async (sources: string[]) => {
    await executeExcludedSourcesOperation(sources, 'add');
  }, [executeExcludedSourcesOperation]);

  /**
   * Removes sources from the excluded list
   */
  const removeExcludedSources = useCallback(async (sources: string[]) => {
    await executeExcludedSourcesOperation(sources, 'remove');
  }, [executeExcludedSourcesOperation]);

  // Cleanup function to prevent state updates after unmount
  const cleanup = useCallback(() => {
    isMountedRef.current = false;
  }, []);

  // Return the hook interface
  return {
    // State
    isProcessing,
    error,
    
    // Operations
    addExcludedSources,
    removeExcludedSources,
    clearError,
  };
}

/**
 * Type exports for use in other modules
 */
export type { UseExcludedSourcesReturn, UseExcludedSourcesOptions };
