import { Skeleton } from "@/components/ui/skeleton";

export function NewsListSkeleton() {
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4">
      <div className="flex items-start space-x-4">
        {/* Thumbnail skeleton */}
        <Skeleton className="w-20 h-16 lg:w-24 lg:h-20 rounded-lg shrink-0" />
        
        {/* Content skeleton */}
        <div className="flex-1 space-y-3">
          {/* Header skeleton */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 flex-1">
              <Skeleton className="h-8 w-8 rounded-full shrink-0" />
              <div className="space-y-1 flex-1">
                <Skeleton className="h-3 w-24" />
                <Skeleton className="h-3 w-16" />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Skeleton className="h-6 w-20" />
              <Skeleton className="h-8 w-8" />
            </div>
          </div>
          
          {/* Title skeleton */}
          <Skeleton className="h-4 w-3/4" />
          
          {/* Content skeleton */}
          <div className="space-y-1">
            <Skeleton className="h-3 w-full" />
            <Skeleton className="h-3 w-2/3" />
          </div>
        </div>
      </div>
    </div>
  );
}