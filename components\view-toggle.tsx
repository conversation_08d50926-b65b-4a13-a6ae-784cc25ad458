"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Grid3X3, List } from "lucide-react";
import { cn } from "@/lib/utils";

interface ViewToggleProps {
  view: "grid" | "list";
  onViewChange: (view: "grid" | "list") => void;
}

export function ViewToggle({ view, onViewChange }: ViewToggleProps) {
  return (
    <div className="flex items-center space-x-0.5 sm:space-x-1 bg-gray-100 p-0.5 sm:p-1 rounded-lg">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onViewChange("grid")}
        className={cn(
          "h-7 w-7 sm:h-8 sm:w-8 p-0 transition-all duration-200 touch-target",
          view === "grid"
            ? "bg-white shadow-xs text-blue-600"
            : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
        )}
        aria-label="Visualização em grade"
      >
        <Grid3X3 className="h-3 w-3 sm:h-4 sm:w-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onViewChange("list")}
        className={cn(
          "h-7 w-7 sm:h-8 sm:w-8 p-0 transition-all duration-200 touch-target",
          view === "list"
            ? "bg-white shadow-xs text-blue-600"
            : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
        )}
        aria-label="Visualização em lista"
      >
        <List className="h-3 w-3 sm:h-4 sm:w-4" />
      </Button>
    </div>
  );
}
